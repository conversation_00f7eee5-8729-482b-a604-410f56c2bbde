###############################################################################
# Build / Install
###############################################################################

ldflags = -X github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/cmd.Version=$(VERSION) \
					-X github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/cmd.Commit=$(COMMIT) \
					-X github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/cmd.Dirty=$(DIRTY)

ldflags += $(LDFLAGS)
ldflags := $(strip $(ldflags))

BUILD_FLAGS := -ldflags '$(ldflags)'

# ビルド&インストール
.PHONY: install
install: buildstatics
	go install ./...

# 開発環境セットアップ
.PHONY: setup
setup: tools download ;

# 動的ファイル生成
.PHONY: buildstatics
buildstatics: tools
	wire ./...

# 依存モジュールダウンロード
.PHONY: download
download:
	go mod download && go mod tidy

.PHONY: tools
tools:
	go install github.com/google/wire/cmd/wire@v0.5.0

.PHONY: run
run:
	docker-compose down && docker-compose up --build

.PHONY: test-adapter
test-adapter:
	ENV=test go test ./internal/adapter -v

.PHONY: test-usecase
test-usecase:
	ENV=test go test ./internal/usecase -v

.PHONY: test-infra
test-infra:
	ENV=test go test ./internal/infrastructure/... -v

.PHONY: test-all
test-all:
	ENV=test go test -coverpkg ./internal/adapter,./internal/usecase,./internal/infrastructure/... -v -coverprofile=coverage.log ./...

.PHONY: test-cover
test-cover:
	ENV=test go tool cover -html=coverage.log -o coverage.html

.PHONY: mockgen
mockgen:
	go generate ./...

.PHONY: clean
clean:
ifeq ($(OS),Windows_NT)
	@echo "Delete bc_monitoring binary..."
	@rm -rf ./tmp/bc_monitoring.exe
else
	@echo "Delete bc_monitoring binary..."
	@rm -rf ./tmp/bc_monitoring
endif

.PHONY: prod-build
prod-build: go.sum install clean
ifeq ($(OS),Windows_NT)
	@echo "building bc_monitoring binary..."
	@go build $(BUILD_FLAGS) -o ./tmp/bc_monitoring.exe ./cmd/bc_monitoring
else
	@echo "Building bc_monitoring binary..."
	@go build $(BUILD_FLAGS) -o ./tmp/bc_monitoring ./cmd/bc_monitoring
endif