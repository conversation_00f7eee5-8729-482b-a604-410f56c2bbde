package entity

import "os"

type Events []Event

type Event struct {
	TransactionHash  string `dynamodbav:"transactionHash"`
	LogIndex         uint   `dynamodbav:"logIndex"`
	Name             string `dynamodbav:"name"`
	IndexedValues    string `dynamodbav:"indexedValues"`
	NonIndexedValues string `dynamodbav:"nonIndexedValues"`
	BlockTimestamp   uint64 `dynamodbav:"blockTimestamp"`
	Log              string `dynamodbav:"log"`
}

func (e Event) Table() string {
	return os.Getenv("EVENTS_TABLE_NAME")
}
