package config

import (
	"log"

	"github.com/kelseyhightower/envconfig"
)

type LocalConfig struct {
	LocalS3Url       string `envconfig:"LOCAL_S3_URL" default:"http://localhost:4566"`
	LocalS3AccessKey string `envconfig:"LOCAL_S3_ACCESS_KEY" default:"access123"`
	LocalS3SecretKey string `envconfig:"LOCAL_S3_SECRET_KEY" default:"secret123"`
}

func GetLocalConfig() *LocalConfig {
	var conf LocalConfig
	if err := envconfig.Process("", &conf); err != nil {
		log.Fatalf("[ERROR] failed to process local config: %v\n", err)
	}
	return &conf
}
