package config

import (
	"os"
)

func GetDynamoDBEndpoint() string {
	return os.Getenv("DYNAMODB_ENDPOINT")
}

func GetDynamoDBRegion() string {
	return os.Getenv("DYNAMODB_REGION")
}

func GetDynamoDBTablePrefix() string {
	return os.Getenv("DYNAMODB_TABLE_NAME_PREFIX")
}

func GetWebsocketURIHost() string {
	return os.Getenv("WEBSOCKET_URI_HOST")
}

func GetWebsocketURIPort() string {
	return os.Getenv("WEBSOCKET_URI_PORT")
}

func GetS3BucketName() string {
	return os.Getenv("S3_BUCKET_NAME")
}

func GetS3Region() string {
	return os.Getenv("S3_REGION")
}

// GetSubscriptionCheckInterval m秒
func GetSubscriptionCheckInterval() string {
	return os.Getenv("SUBSCRIPTION_CHECK_INTERVAL")
}

// GetAllowableBlockTimestampDIffSec 検知したBlockTimestampと実時間との差分で許容できる秒数
func GetAllowableBlockTimestampDIffSec() string {
	return os.Getenv("ALLOWABLE_BLOCK_TIMESTAMP_DIFF_SEC")
}

func GetEnvironment() string {
	return os.Getenv("ENV")
}

func GetABIFormat() string {
	return os.Getenv("ABI_FORMAT")
}
