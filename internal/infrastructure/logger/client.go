package logger

import (
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/config"
	"github.com/sirupsen/logrus"
	"os"
	"strings"
)

// TODO: loggerをinterfaceでwrapする方がアーキテクチャとしてはベターだが、
// 	PoC段階では冗長且つライブラリ利用の利便性も落ちるため保留
//type Logger interface {
//	Infoln(args ...interface{})
//	Errorln(args ...interface{})
//	Fatalln(args ...interface{})
//	Infof(string, ...interface{})
//	Errorf(string, ...interface{})
//	Fatalf(string, ...interface{})
//}
//
//type logger struct {
//	cli *logrus.Entry
//}

func NewLogger(uuid string) *logrus.Entry {
	l := &logrus.Logger{
		Out: os.Stdout,
		Formatter: &logrus.TextFormatter{
			DisableColors:   true,
			TimestampFormat: "2006-01-02 15:04:05",
			FullTimestamp:   true,
		},
	}
	switch strings.ToLower(config.GetEnvironment()) {
	case "prod":
		l.<PERSON>(logrus.InfoLevel)
	case "local":
		l.<PERSON>(logrus.DebugLevel)
	case "test":
		l.SetLevel(logrus.PanicLevel)
	default:
		l.SetLevel(logrus.TraceLevel)
	}
	l.SetReportCaller(true)
	return l.WithFields(logrus.Fields{"process_uuid": uuid})
}

//func (l *logger) Infoln(args ...interface{}) {
//	l.cli.Logln(logrus.InfoLevel, args...)
//}
//
//func (l *logger) Errorln(args ...interface{}) {
//	l.cli.Logln(logrus.ErrorLevel, args...)
//}
//
//func (l *logger) Fatalln(args ...interface{}) {
//	l.cli.Logln(logrus.FatalLevel, args...)
//	l.cli.Exit(1)
//}
//
//func (l *logger) Infof(format string, args ...interface{}) {
//	l.cli.Infof(format, args)
//}
//
//func (l *logger) Errorf(format string, args ...interface{}) {
//	l.cli.Logf(logrus.ErrorLevel, format, args...)
//}
//
//func (l *logger) Fatalf(format string, args ...interface{}) {
//	l.cli.Logf(logrus.FatalLevel, format, args...)
//	l.cli.Exit(1)
//}
