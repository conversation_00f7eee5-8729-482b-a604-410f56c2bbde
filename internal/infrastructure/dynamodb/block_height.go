package dynamodb

import (
	"context"
	"github.com/sirupsen/logrus"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
)

type DynamoBlockHeightDao struct {
	l       *logrus.Entry
	context context.Context
}

func NewDynamoBlockHeightDao(l *logrus.Entry, ctx context.Context) *DynamoBlockHeightDao {
	return &DynamoBlockHeightDao{l, ctx}
}

func (dao *DynamoBlockHeightDao) PutItem(blockHeight entity.BlockHeight) error {
	client, err := newConnection()
	if err != nil {
		return err
	}
	defer releaseConnection(client)
	blockHeight.ID = 1
	input, err := serializePutItemInput(blockHeight)
	if err != nil {
		return err
	}
	_, err = client.PutItem(dao.context, input)
	return err
}

func (dao *DynamoBlockHeightDao) GetItem() ([]entity.BlockHeight, error) {
	client, err := newConnection()
	if err != nil {
		return nil, err
	}
	defer releaseConnection(client)
	params := dynamodb.QueryInput{
		KeyConditionExpression: aws.String("id = :id"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":id": &types.AttributeValueMemberN{Value: "1"},
		},
	}
	input := getItemInput(entity.BlockHeight{}, params)
	ret, err := client.Query(dao.context, input)
	if err != nil {
		return nil, err
	}
	l := &[]entity.BlockHeight{}
	if err := attributevalue.UnmarshalListOfMaps(ret.Items, l); err != nil {
		return nil, err
	}

	return *l, nil

}
