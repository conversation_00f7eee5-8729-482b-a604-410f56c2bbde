package dynamodb

import (
	"context"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
	"github.com/sirupsen/logrus"
)

type DynamoEventDao struct {
	context context.Context
	l       *logrus.Entry
}

func NewDynamoEventDao(ctx context.Context, l *logrus.Entry) *DynamoEventDao {
	return &DynamoEventDao{ctx, l}
}

func (dao *DynamoEventDao) PutItem(event entity.Event) error {
	client, err := newConnection()
	if err != nil {
		return err
	}
	defer releaseConnection(client)

	input, err := serializePutItemInput(event)
	if err != nil {
		return err
	}
	_, err = client.PutItem(dao.context, input)
	return err
}
