package ethereum

import (
	"testing"
	"time"
)

func TestEthEventLogDao_isDelayedToDetectBlockHeader(t *testing.T) {
	type args struct {
		blockCreatedTimestamp uint64
		allowableSec          uint64
	}
	tests := []struct {
		name        string
		args        args
		mockTimeNow time.Time
		want1       bool
		want2       uint64
		want3       uint64
	}{
		{
			name: "should return true when diff is allowance +1sec",
			args: args{
				blockCreatedTimestamp: 1633415880,
				allowableSec:          2,
			},
			mockTimeNow: time.Unix(1633415883, 0),
			want1:       true,
			want2:       3,
			want3:       1633415883,
		},
		{
			name: "should return false when diff is allowance -1sec",
			args: args{
				blockCreatedTimestamp: 1633415882,
				allowableSec:          2,
			},
			mockTimeNow: time.Unix(1633415883, 0),
			want1:       false,
			want2:       1,
			want3:       1633415883,
		},
		{
			name: "should return false when diff is exactly same as allowance",
			args: args{
				blockCreatedTimestamp: 1633415881,
				allowableSec:          2,
			},
			mockTimeNow: time.Unix(1633415883, 0),
			want1:       false,
			want2:       2,
			want3:       1633415883,
		},
		{
			name: "should return false when diff has nothing",
			args: args{
				blockCreatedTimestamp: 1633415883,
				allowableSec:          2,
			},
			mockTimeNow: time.Unix(1633415883, 0),
			want1:       false,
			want2:       0,
			want3:       1633415883,
		},
		{
			name: "should return false when blockCreatedTimestamp is 1sec ahead of timeNow",
			args: args{
				blockCreatedTimestamp: 1633415884,
				allowableSec:          2,
			},
			mockTimeNow: time.Unix(1633415883, 0),
			want1:       false,
			want2:       0,
			want3:       1633415883,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// isDelayedToDetectBlockHeader関数のtimeNowをargsで指定したmockTimeNowで書き換える
			timeNow = func() time.Time {
				return tt.mockTimeNow
			}
			dao := EthEventLogDao{}
			got1, got2, got3 := dao.isDelayedToDetectBlockHeader(tt.args.blockCreatedTimestamp, tt.args.allowableSec)
			if got1 != tt.want1 {
				t.Errorf("isDelayedToDetectBlockHeader() got1 = %v, want %v", got1, tt.want1)
			}
			if got2 != tt.want2 {
				t.Errorf("isDelayedToDetectBlockHeader() got2 = %v, want %v", got2, tt.want2)
			}
			if got3 != tt.want3 {
				t.Errorf("isDelayedToDetectBlockHeader() got3 = %v, want %v", got3, tt.want3)
			}
		})
	}
}
