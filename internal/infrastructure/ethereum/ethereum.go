package ethereum

import (
	"context"
	"errors"
	"fmt"
	"math/big"
	"strings"

	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/config"
	geth "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/ethereum/go-ethereum/rpc"
)

type contractEvents struct {
	contractName string
	events       map[string]abi.Event
}

var (
	contractEventStore = make(map[string]contractEvents)
	contractAddresses  []common.Address
	ErrNoAbiEventFound = errors.New("event definition not found in abi")
)

func AppendContractEventStore(address string, contractName string, events map[string]abi.Event) {
	contractEventStore[address] = contractEvents{
		contractName: contractName,
		events:       events,
	}
}

func AppendContractAddresses(address string) {
	contractAddresses = append(contractAddresses, common.HexToAddress(address))
}

func newWSClient(ctx context.Context) (*ethclient.Client, error) {
	endpoint := fmt.Sprintf("ws://%s:%s", config.GetWebsocketURIHost(), config.GetWebsocketURIPort())
	return ethclient.DialContext(ctx, endpoint)
}

func filterFrom(i int64) geth.FilterQuery {
	return geth.FilterQuery{
		FromBlock: big.NewInt(i),
		ToBlock:   big.NewInt(rpc.LatestBlockNumber.Int64()),
		Addresses: contractAddresses,
	}
}

func getABIEventByLog(log types.Log) (abi.Event, error) {
	eventId := strings.ToLower(log.Topics[0].String())
	logAddress := strings.ToLower(log.Address.String())
	events := contractEventStore[logAddress].events
	for _, e := range events {
		if strings.ToLower(e.ID.String()) == eventId {
			return e, nil
		}
	}
	return abi.Event{}, ErrNoAbiEventFound
}
