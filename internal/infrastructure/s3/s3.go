package s3

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/aws"
	awsConfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/config"
)

func newS3Client() (*s3.Client, error) {
	var cfg aws.Config
	var err error
	if config.GetEnvironment() == "prod" {
		// クラウド環境設定
		cfg, err = awsConfig.LoadDefaultConfig(
			context.TODO(),
		)

	} else {
		// ローカル環境設定
		var localConf = config.GetLocalConfig()
		resolver := aws.EndpointResolverFunc(func(service, region string) (aws.Endpoint, error) {
			return aws.Endpoint{
				URL:               localConf.LocalS3Url,
				SigningRegion:     config.GetS3Region(),
				HostnameImmutable: true,
			}, nil
		})
		cfg = aws.Config{
			Region:           config.GetS3Region(),
			Credentials:      credentials.NewStaticCredentialsProvider(localConf.LocalS3AccessKey, localConf.LocalS3SecretKey, ""),
			EndpointResolver: resolver,
		}
	}
	c := s3.NewFromConfig(cfg)
	return c, err
}
