//go:generate mockgen -source=$GOFILE -package=mock -destination=./mock/mock_$GOFILE
package usecase

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
)

type EventLogRepository interface {
	Subscribe(context.Context) (<-chan entity.Transaction, error)
	GetFilterLogs(context.Context, uint64) (<-chan entity.Transaction, error)
}

type EventRepository interface {
	Save(entity.Event) bool
}

type BlockHeightRepository interface {
	Save(entity.BlockHeight) bool
	Get() (entity.BlockHeight, error)
}

type S3AbiDaoRepository interface {
	ListCommonPrefixesObjects(bucketName string) (*s3.ListObjectsV2Output, error)
	ListObjects(bucketName string, prefix string) (*s3.ListObjectsV2Output, error)
	GetObject(bucketName string, key string) (*s3.GetObjectOutput, error)
}
