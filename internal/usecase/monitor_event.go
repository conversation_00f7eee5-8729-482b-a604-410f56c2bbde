package usecase

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/config"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
)

type MonitorEventInteractor struct {
	ctx context.Context
	l   *logrus.Entry
	EventLogRepository
	EventRepository
	BlockHeightRepository
}

func NewMonitorEventInteractor(context context.Context, l *logrus.Entry, eventLogRepository EventLogRepository, eventRepository EventRepository, blockHeightRepository BlockHeightRepository) *MonitorEventInteractor {
	return &MonitorEventInteractor{
		ctx:                   context,
		l:                     l,
		EventLogRepository:    eventLogRepository,
		EventRepository:       eventRepository,
		BlockHeightRepository: blockHeightRepository,
	}
}

func (m MonitorEventInteractor) Execute() error {

	checkInterval, err := strconv.Atoi(config.GetSubscriptionCheckInterval())
	if err != nil {
		m.l.Errorf("faild to convert checkInterval:  %s\n", err)
		return err
	}
	for {
		var exBlockHeight entity.BlockHeight
		blockHeight, err := m.BlockHeightRepository.Get()
		if err != nil {
			m.l.Errorf("failed to get blockheight %s", err)
			return err
		}
		m.l.Infof("get blockheight: %#v", blockHeight.BlockNumber)
		ctx, cancel := context.WithCancel(m.ctx)
		transactions, err := m.EventLogRepository.Subscribe(ctx)
		if err != nil {
			return err
		}
		pendingTransactions, err := m.EventLogRepository.GetFilterLogs(ctx, blockHeight.BlockNumber+1)
		if err != nil {
			return err
		}
		for {
			p, ok := <-pendingTransactions
			m.l.Infof("pending block height Number is: %d", p.BlockHeight.BlockNumber)
			m.l.Infof("exBlockHeight is: %d", exBlockHeight.BlockNumber)
			if exBlockHeight.BlockNumber != 0 && exBlockHeight.BlockNumber != p.BlockHeight.BlockNumber {
				if !m.savePendingTransactionBlockNumber(exBlockHeight) {
					cancel()
					time.Sleep(time.Millisecond * time.Duration(checkInterval))
					goto RETRY
				}
			}
			if !ok {
				m.l.Infof("pending transactions channel was closed")
				goto TRANSACTION
			}
			if p.BlockHeight.BlockNumber == 0 {
				m.l.Infof("pending block height Number is zero")
				cancel()
				time.Sleep(time.Millisecond * time.Duration(checkInterval))
				goto RETRY
			} else {
				if !m.savePendingTransaction(p) {
					cancel()
					time.Sleep(time.Millisecond * time.Duration(checkInterval))
					goto RETRY
				}
				m.l.Infof("exBlockHeight updated to: %#v", exBlockHeight)
				exBlockHeight = p.BlockHeight
			}
		}

	TRANSACTION:
		for {
			t := <-transactions
			if t.BlockHeight.BlockNumber == 0 {
				m.l.Warnln("block height Number is zero")
				cancel()
				time.Sleep(time.Millisecond * time.Duration(checkInterval))
				goto RETRY
			}
			if !m.saveTransaction(t) {
				cancel()
				time.Sleep(time.Millisecond * time.Duration(checkInterval))
				goto RETRY
			}
			if err != nil {
				//cancel()
				return err
			}
		}
	RETRY:
	}
}

func (m MonitorEventInteractor) saveTransaction(tx entity.Transaction) bool {
	// TODO: 複数同時INSERT
	// 	https://decurret.atlassian.net/browse/DCFC-5533
	for _, e := range tx.Events {
		if e.TransactionHash == "" {
			m.l.Errorln("event transaction hash is zero")
			return false
		}
		traceId := m.fetchTraceId(e.NonIndexedValues)
		m.l = m.l.WithFields(logrus.Fields{
			"event_name":      e.Name,
			"tx_hash":         e.TransactionHash,
			"block_height":    tx.BlockHeight,
			"log_index":       e.LogIndex,
			"block_timestamp": e.BlockTimestamp,
			"trace_id":        traceId,
		})
		if !m.EventRepository.Save(e) {
			m.l.Errorln("failure to register event")
			return false
		}
		m.l.Infof("success to register event")
	}
	if !m.BlockHeightRepository.Save(tx.BlockHeight) {
		m.l.Errorf("failure to register block number")
		return false
	}
	m.l.Infof("success to register block number")
	return true
}

func (m MonitorEventInteractor) savePendingTransaction(tx entity.Transaction) bool {
	for _, e := range tx.Events {
		if e.TransactionHash == "" {
			m.l.Errorln("event transaction hash is zero")
			return false
		}
		traceId := m.fetchTraceId(e.NonIndexedValues)
		m.l = m.l.WithFields(logrus.Fields{
			"event_name":      e.Name,
			"tx_hash":         e.TransactionHash,
			"block_height":    tx.BlockHeight,
			"log_index":       e.LogIndex,
			"block_timestamp": e.BlockTimestamp,
			"trace_id":        traceId,
		})
		if !m.EventRepository.Save(e) {
			m.l.Errorln("failure to register event")
			return false
		}
		m.l.Infof("success to register event")
	}
	return true
}

func (m MonitorEventInteractor) savePendingTransactionBlockNumber(blockHeight entity.BlockHeight) bool {
	if !m.BlockHeightRepository.Save(blockHeight) {
		m.l.WithField("block number", blockHeight.BlockNumber).Errorf("failure to register block number")
		return false
	}
	m.l.WithField("block number", blockHeight.BlockNumber).Infof("success to register block number")
	return true
}

type parsedTraceId struct {
	TraceId []byte `json:"traceId"`
}

func (m MonitorEventInteractor) fetchTraceId(nonIndexedValues string) string {
	var parsed parsedTraceId
	err := json.Unmarshal([]byte(nonIndexedValues), &parsed)
	if err != nil {
		m.l.Errorln(err)
		return ""
	}
	if parsed.TraceId == nil || len(parsed.TraceId) == 0 {
		return ""
	}
	var bytes []byte
	for _, v := range parsed.TraceId {
		if v != 0 {
			bytes = append(bytes, v)
		}
	}
	return string(bytes)
}
