// Code generated by MockGen. DO NOT EDIT.
// Source: repo.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	s3 "github.com/aws/aws-sdk-go-v2/service/s3"
	entity "github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockEventLogRepository is a mock of EventLogRepository interface.
type MockEventLogRepository struct {
	ctrl     *gomock.Controller
	recorder *MockEventLogRepositoryMockRecorder
}

// MockEventLogRepositoryMockRecorder is the mock recorder for MockEventLogRepository.
type MockEventLogRepositoryMockRecorder struct {
	mock *MockEventLogRepository
}

// NewMockEventLogRepository creates a new mock instance.
func NewMockEventLogRepository(ctrl *gomock.Controller) *MockEventLogRepository {
	mock := &MockEventLogRepository{ctrl: ctrl}
	mock.recorder = &MockEventLogRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEventLogRepository) EXPECT() *MockEventLogRepositoryMockRecorder {
	return m.recorder
}

// GetFilterLogs mocks base method.
func (m *MockEventLogRepository) GetFilterLogs(arg0 context.Context, arg1 uint64) (<-chan entity.Transaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFilterLogs", arg0, arg1)
	ret0, _ := ret[0].(<-chan entity.Transaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFilterLogs indicates an expected call of GetFilterLogs.
func (mr *MockEventLogRepositoryMockRecorder) GetFilterLogs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilterLogs", reflect.TypeOf((*MockEventLogRepository)(nil).GetFilterLogs), arg0, arg1)
}

// Subscribe mocks base method.
func (m *MockEventLogRepository) Subscribe(arg0 context.Context) (<-chan entity.Transaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Subscribe", arg0)
	ret0, _ := ret[0].(<-chan entity.Transaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Subscribe indicates an expected call of Subscribe.
func (mr *MockEventLogRepositoryMockRecorder) Subscribe(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Subscribe", reflect.TypeOf((*MockEventLogRepository)(nil).Subscribe), arg0)
}

// MockEventRepository is a mock of EventRepository interface.
type MockEventRepository struct {
	ctrl     *gomock.Controller
	recorder *MockEventRepositoryMockRecorder
}

// MockEventRepositoryMockRecorder is the mock recorder for MockEventRepository.
type MockEventRepositoryMockRecorder struct {
	mock *MockEventRepository
}

// NewMockEventRepository creates a new mock instance.
func NewMockEventRepository(ctrl *gomock.Controller) *MockEventRepository {
	mock := &MockEventRepository{ctrl: ctrl}
	mock.recorder = &MockEventRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEventRepository) EXPECT() *MockEventRepositoryMockRecorder {
	return m.recorder
}

// Save mocks base method.
func (m *MockEventRepository) Save(arg0 entity.Event) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockEventRepositoryMockRecorder) Save(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockEventRepository)(nil).Save), arg0)
}

// MockBlockHeightRepository is a mock of BlockHeightRepository interface.
type MockBlockHeightRepository struct {
	ctrl     *gomock.Controller
	recorder *MockBlockHeightRepositoryMockRecorder
}

// MockBlockHeightRepositoryMockRecorder is the mock recorder for MockBlockHeightRepository.
type MockBlockHeightRepositoryMockRecorder struct {
	mock *MockBlockHeightRepository
}

// NewMockBlockHeightRepository creates a new mock instance.
func NewMockBlockHeightRepository(ctrl *gomock.Controller) *MockBlockHeightRepository {
	mock := &MockBlockHeightRepository{ctrl: ctrl}
	mock.recorder = &MockBlockHeightRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBlockHeightRepository) EXPECT() *MockBlockHeightRepositoryMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockBlockHeightRepository) Get() (entity.BlockHeight, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get")
	ret0, _ := ret[0].(entity.BlockHeight)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockBlockHeightRepositoryMockRecorder) Get() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockBlockHeightRepository)(nil).Get))
}

// Save mocks base method.
func (m *MockBlockHeightRepository) Save(arg0 entity.BlockHeight) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockBlockHeightRepositoryMockRecorder) Save(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockBlockHeightRepository)(nil).Save), arg0)
}

// MockS3AbiDaoRepository is a mock of S3AbiDaoRepository interface.
type MockS3AbiDaoRepository struct {
	ctrl     *gomock.Controller
	recorder *MockS3AbiDaoRepositoryMockRecorder
}

// MockS3AbiDaoRepositoryMockRecorder is the mock recorder for MockS3AbiDaoRepository.
type MockS3AbiDaoRepositoryMockRecorder struct {
	mock *MockS3AbiDaoRepository
}

// NewMockS3AbiDaoRepository creates a new mock instance.
func NewMockS3AbiDaoRepository(ctrl *gomock.Controller) *MockS3AbiDaoRepository {
	mock := &MockS3AbiDaoRepository{ctrl: ctrl}
	mock.recorder = &MockS3AbiDaoRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockS3AbiDaoRepository) EXPECT() *MockS3AbiDaoRepositoryMockRecorder {
	return m.recorder
}

// GetObject mocks base method.
func (m *MockS3AbiDaoRepository) GetObject(bucketName, key string) (*s3.GetObjectOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetObject", bucketName, key)
	ret0, _ := ret[0].(*s3.GetObjectOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObject indicates an expected call of GetObject.
func (mr *MockS3AbiDaoRepositoryMockRecorder) GetObject(bucketName, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObject", reflect.TypeOf((*MockS3AbiDaoRepository)(nil).GetObject), bucketName, key)
}

// ListCommonPrefixesObjects mocks base method.
func (m *MockS3AbiDaoRepository) ListCommonPrefixesObjects(bucketName string) (*s3.ListObjectsV2Output, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCommonPrefixesObjects", bucketName)
	ret0, _ := ret[0].(*s3.ListObjectsV2Output)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCommonPrefixesObjects indicates an expected call of ListCommonPrefixesObjects.
func (mr *MockS3AbiDaoRepositoryMockRecorder) ListCommonPrefixesObjects(bucketName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCommonPrefixesObjects", reflect.TypeOf((*MockS3AbiDaoRepository)(nil).ListCommonPrefixesObjects), bucketName)
}

// ListObjects mocks base method.
func (m *MockS3AbiDaoRepository) ListObjects(bucketName, prefix string) (*s3.ListObjectsV2Output, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListObjects", bucketName, prefix)
	ret0, _ := ret[0].(*s3.ListObjectsV2Output)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListObjects indicates an expected call of ListObjects.
func (mr *MockS3AbiDaoRepositoryMockRecorder) ListObjects(bucketName, prefix interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListObjects", reflect.TypeOf((*MockS3AbiDaoRepository)(nil).ListObjects), bucketName, prefix)
}
