//go:generate mockgen -source=$GOFILE -package=mock -destination=./mock/mock_$GOFILE
package adapter

import (
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/sirupsen/logrus"
)

type S3AbiDao interface {
	ListCommonPrefixesObjects(bucketName string) (*s3.ListObjectsV2Output, error)
	ListObjects(bucketName string, prefix string) (*s3.ListObjectsV2Output, error)
	GetObject(bucketName string, key string) (*s3.GetObjectOutput, error)
}

type S3AbiDaoRepository struct {
	l        *logrus.Entry
	s3AbiDao S3AbiDao
}

func NewS3AbiDaoRepository(l *logrus.Entry, s3AbiDao S3AbiDao) *S3AbiDaoRepository {
	return &S3AbiDaoRepository{l, s3AbiDao}
}

func (repo *S3AbiDaoRepository) ListCommonPrefixesObjects(bucketName string) (*s3.ListObjectsV2Output, error) {
	return repo.s3AbiDao.ListCommonPrefixesObjects(bucketName)
}

func (repo *S3AbiDaoRepository) ListObjects(bucketName string, prefix string) (*s3.ListObjectsV2Output, error) {
	return repo.s3AbiDao.ListObjects(bucketName, prefix)
}

func (repo *S3AbiDaoRepository) GetObject(bucketName string, key string) (*s3.GetObjectOutput, error) {
	return repo.s3AbiDao.GetObject(bucketName, key)
}
