//go:generate mockgen -source=$GOFILE -package=mock -destination=./mock/mock_$GOFILE
package adapter

import (
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
	"github.com/sirupsen/logrus"
)

type EventDao interface {
	PutItem(entity.Event) error
}

type EventRepository struct {
	l        *logrus.Entry
	eventDao EventDao
}

func NewEventRepository(l *logrus.Entry, eventDao EventDao) *EventRepository {
	return &EventRepository{l, eventDao}
}

func (repo *EventRepository) Save(event entity.Event) bool {
	err := repo.eventDao.PutItem(event)
	if err != nil {
		repo.l.Errorln(err)
		return false
	}
	return true
}
