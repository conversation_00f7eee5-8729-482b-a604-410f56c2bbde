// Code generated by MockGen. DO NOT EDIT.
// Source: abi.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	s3 "github.com/aws/aws-sdk-go-v2/service/s3"
	gomock "go.uber.org/mock/gomock"
)

// MockS3AbiDao is a mock of S3AbiDao interface.
type MockS3AbiDao struct {
	ctrl     *gomock.Controller
	recorder *MockS3AbiDaoMockRecorder
}

// MockS3AbiDaoMockRecorder is the mock recorder for MockS3AbiDao.
type MockS3AbiDaoMockRecorder struct {
	mock *MockS3AbiDao
}

// NewMockS3AbiDao creates a new mock instance.
func NewMockS3AbiDao(ctrl *gomock.Controller) *MockS3AbiDao {
	mock := &MockS3AbiDao{ctrl: ctrl}
	mock.recorder = &MockS3AbiDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockS3AbiDao) EXPECT() *MockS3AbiDaoMockRecorder {
	return m.recorder
}

// GetObject mocks base method.
func (m *MockS3AbiDao) GetObject(bucketName, key string) (*s3.GetObjectOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetObject", bucketName, key)
	ret0, _ := ret[0].(*s3.GetObjectOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObject indicates an expected call of GetObject.
func (mr *MockS3AbiDaoMockRecorder) GetObject(bucketName, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObject", reflect.TypeOf((*MockS3AbiDao)(nil).GetObject), bucketName, key)
}

// ListCommonPrefixesObjects mocks base method.
func (m *MockS3AbiDao) ListCommonPrefixesObjects(bucketName string) (*s3.ListObjectsV2Output, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCommonPrefixesObjects", bucketName)
	ret0, _ := ret[0].(*s3.ListObjectsV2Output)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCommonPrefixesObjects indicates an expected call of ListCommonPrefixesObjects.
func (mr *MockS3AbiDaoMockRecorder) ListCommonPrefixesObjects(bucketName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCommonPrefixesObjects", reflect.TypeOf((*MockS3AbiDao)(nil).ListCommonPrefixesObjects), bucketName)
}

// ListObjects mocks base method.
func (m *MockS3AbiDao) ListObjects(bucketName, prefix string) (*s3.ListObjectsV2Output, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListObjects", bucketName, prefix)
	ret0, _ := ret[0].(*s3.ListObjectsV2Output)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListObjects indicates an expected call of ListObjects.
func (mr *MockS3AbiDaoMockRecorder) ListObjects(bucketName, prefix interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListObjects", reflect.TypeOf((*MockS3AbiDao)(nil).ListObjects), bucketName, prefix)
}
