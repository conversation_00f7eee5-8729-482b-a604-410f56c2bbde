// Code generated by MockGen. DO NOT EDIT.
// Source: logs.go
//
// Generated by this command:
//
//	mockgen -source=logs.go -package=mock -destination=./mock/mock_logs.go
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	entity "github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockEventLogDao is a mock of EventLogDao interface.
type MockEventLogDao struct {
	ctrl     *gomock.Controller
	recorder *MockEventLogDaoMockRecorder
}

// MockEventLogDaoMockRecorder is the mock recorder for MockEventLogDao.
type MockEventLogDaoMockRecorder struct {
	mock *MockEventLogDao
}

// NewMockEventLogDao creates a new mock instance.
func NewMockEventLogDao(ctrl *gomock.Controller) *MockEventLogDao {
	mock := &MockEventLogDao{ctrl: ctrl}
	mock.recorder = &MockEventLogDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEventLogDao) EXPECT() *MockEventLogDaoMockRecorder {
	return m.recorder
}

// GetPendingTransactions mocks base method.
func (m *MockEventLogDao) GetPendingTransactions(arg0 context.Context, arg1 uint64) (<-chan entity.Transaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPendingTransactions", arg0, arg1)
	ret0, _ := ret[0].(<-chan entity.Transaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPendingTransactions indicates an expected call of GetPendingTransactions.
func (mr *MockEventLogDaoMockRecorder) GetPendingTransactions(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPendingTransactions", reflect.TypeOf((*MockEventLogDao)(nil).GetPendingTransactions), arg0, arg1)
}

// SubscribeAll mocks base method.
func (m *MockEventLogDao) SubscribeAll(arg0 context.Context) (<-chan entity.Transaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubscribeAll", arg0)
	ret0, _ := ret[0].(<-chan entity.Transaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubscribeAll indicates an expected call of SubscribeAll.
func (mr *MockEventLogDaoMockRecorder) SubscribeAll(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubscribeAll", reflect.TypeOf((*MockEventLogDao)(nil).SubscribeAll), arg0)
}
