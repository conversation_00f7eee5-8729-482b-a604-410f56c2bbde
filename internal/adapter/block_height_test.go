package adapter

import (
	"fmt"
	"testing"

	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/logger"

	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/adapter/mock"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
	"go.uber.org/mock/gomock"
)

func TestBlockHeightRepository_Save(t *testing.T) {
	l := logger.NewLogger("test")

	t.Run("保存処理を実行したとき、DAOが一度だけ呼び出されること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		blockHeight := entity.BlockHeight{BlockNumber: 1111}

		mockBlockHeightDao := mock.NewMockBlockHeightDao(ctrl)
		mockBlockHeightDao.EXPECT().PutItem(gomock.Any()).Do(func(e entity.BlockHeight) {
			if blockHeight.BlockNumber != e.BlockNumber {
				t.Errorf("invalid blockNumber is given, expect %v / actual %v\n", blockHeight.BlockNumber, e.BlockNumber)
			}
		}).Times(1)

		blockHeightRepository := NewBlockHeightRepository(l, mockBlockHeightDao)
		if !blockHeightRepository.Save(blockHeight) {
			t.Errorf("test failed: fail to save blockHeight")
		}
	})

	t.Run("保存処理に失敗した場合、falseが返ること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		blockHeight := entity.BlockHeight{BlockNumber: 1111}

		mockBlockHeightDao := mock.NewMockBlockHeightDao(ctrl)
		mockBlockHeightDao.EXPECT().PutItem(blockHeight).Return(fmt.Errorf("error mock")).Times(1)

		blockHeightRepository := NewBlockHeightRepository(l, mockBlockHeightDao)
		if blockHeightRepository.Save(blockHeight) {
			t.Errorf("test failed: success to save blockHeight\n")
		}
	})
}

func TestBlockHeightRepository_Get(t *testing.T) {
	l := logger.NewLogger("test")

	t.Run("取得処理を実行したとき、DAOが一度だけ呼び出されること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		blockHeight := []entity.BlockHeight{{BlockNumber: 1111}}

		mockBlockHeightDao := mock.NewMockBlockHeightDao(ctrl)
		mockBlockHeightDao.EXPECT().GetItem().Return(blockHeight, nil).Times(1)

		blockHeightRepository := NewBlockHeightRepository(l, mockBlockHeightDao)
		except, err := blockHeightRepository.Get()
		if except.BlockNumber != blockHeight[0].BlockNumber {
			t.Errorf("test failed: success to get blockHeight")
		}
		if err != nil {
			t.Errorf("test failed: success to get blockHeight")
		}
	})

	t.Run("取得処理に失敗した場合、ゼロ値のBlockHeightとerrorが返ること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		blockHeight := entity.BlockHeight{}

		mockBlockHeightDao := mock.NewMockBlockHeightDao(ctrl)
		mockBlockHeightDao.EXPECT().GetItem().Return(nil, fmt.Errorf("error mock")).Times(1)

		blockHeightRepository := NewBlockHeightRepository(l, mockBlockHeightDao)
		except, err := blockHeightRepository.Get()
		if except != blockHeight {
			t.Errorf("test failed: fail to get blockHeight\n")
		}
		if err == nil {
			t.Errorf("test failed: fail to get blockHeight\n")
		}
	})
}
