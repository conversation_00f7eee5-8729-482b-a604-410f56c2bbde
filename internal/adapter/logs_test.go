package adapter

import (
	"context"
	"fmt"
	"testing"

	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/logger"

	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/adapter/mock"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
	"go.uber.org/mock/gomock"
)

func TestEventLogRepository_Subscribe(t *testing.T) {
	l := logger.NewLogger("test")
	t.Run("イベントが取得できること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()
		transactions := make(chan entity.Transaction)
		mockEventLogDao := mock.NewMockEventLogDao(ctrl)
		mockEventLogDao.EXPECT().SubscribeAll(ctx).Return(transactions, nil)

		eventLogRepository := NewEventLogRepository(l, mockEventLogDao)
		if o, err := eventLogRepository.Subscribe(ctx); transactions != o {
			t.Errorf("invalid transactions channel returned, expected %v / actual %v / err %v", transactions, o, err)
		}
	})

	t.Run("DAOでエラーが発生している場合、nilが返却されること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()
		transactions := make(chan entity.Transaction)
		mockEventLogDao := mock.NewMockEventLogDao(ctrl)
		mockEventLogDao.EXPECT().SubscribeAll(ctx).Return(transactions, fmt.Errorf("mock error"))

		eventLogRepository := NewEventLogRepository(l, mockEventLogDao)
		if o, err := eventLogRepository.Subscribe(ctx); o != nil {
			t.Errorf("return transaction, expected %v / actual %v / err %v", nil, o, err)
		}
	})

	t.Run("イベント取得失敗時にnilが返ること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()
		mockEventLogDao := mock.NewMockEventLogDao(ctrl)
		mockEventLogDao.EXPECT().SubscribeAll(ctx).Return(nil, nil)

		eventLogRepository := NewEventLogRepository(l, mockEventLogDao)
		if o, err := eventLogRepository.Subscribe(ctx); o != nil {
			t.Errorf("return transaction, expected %v / actual %v / err %v", nil, o, err)
		}
	})
}
