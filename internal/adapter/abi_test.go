package adapter

import (
	"fmt"
	"testing"

	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/adapter/mock"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/logger"
	"go.uber.org/mock/gomock"
)

func TestS3AbiDaoRepository_ListCommonPrefixesObjects(t *testing.T) {
	l := logger.NewLogger("test")
	t.Run("ListCommonPrefixesObjectsを実行したとき、DAOが一度だけ呼び出されること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		testBucket := "test-bucket"

		mockDao := mock.NewMockS3AbiDao(ctrl)
		mockDao.EXPECT().ListCommonPrefixesObjects(gomock.Any()).Do(func(bucketName string) {
			if testBucket != bucketName {
				t.Errorf("invalid bucketName is given, expect %s / actual %s\n", testBucket, bucketName)
			}
		}).Times(1)

		daoRepository := NewS3AbiDaoRepository(l, mockDao)
		if _, err := daoRepository.ListCommonPrefixesObjects(testBucket); err != nil {
			t.Errorf("test failed: fail to list objects")
		}
	})

	t.Run("ListCommonPrefixesObjectsに失敗した場合、falseが返ること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		testBucket := "test-bucket"

		mockDao := mock.NewMockS3AbiDao(ctrl)
		mockDao.EXPECT().ListCommonPrefixesObjects(testBucket).Return(nil, fmt.Errorf("error mock")).Times(1)

		daoRepository := NewS3AbiDaoRepository(l, mockDao)
		if _, err := daoRepository.ListCommonPrefixesObjects(testBucket); err == nil {
			t.Errorf("test failed: success to list objects\n")
		}
	})
}

func TestS3AbiDaoRepository_ListObjects(t *testing.T) {
	l := logger.NewLogger("test")
	t.Run("ListObjectsを実行したとき、DAOが一度だけ呼び出されること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		testBucket := "test-bucket"
		prefix := "3000"

		mockDao := mock.NewMockS3AbiDao(ctrl)
		mockDao.EXPECT().ListObjects(gomock.Any(), gomock.Any()).Do(func(bucketName string, prefix string) {
			if testBucket != bucketName {
				t.Errorf("invalid bucketName is given, expect %s / actual %s\n", testBucket, bucketName)
			}
		}).Times(1)

		daoRepository := NewS3AbiDaoRepository(l, mockDao)
		if _, err := daoRepository.ListObjects(testBucket, prefix); err != nil {
			t.Errorf("test failed: fail to list objects")
		}
	})

	t.Run("ListObjectsに失敗した場合、falseが返ること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		testBucket := "test-bucket"
		prefix := "3000"

		mockDao := mock.NewMockS3AbiDao(ctrl)
		mockDao.EXPECT().ListObjects(testBucket, prefix).Return(nil, fmt.Errorf("error mock")).Times(1)

		daoRepository := NewS3AbiDaoRepository(l, mockDao)
		if _, err := daoRepository.ListObjects(testBucket, prefix); err == nil {
			t.Errorf("test failed: success to list objects\n")
		}
	})
}

func TestS3AbiDaoRepository_GetObject(t *testing.T) {
	l := logger.NewLogger("test")
	t.Run("GetObjectを実行したとき、DAOが一度だけ呼び出されること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		testBucket := "test-bucket"
		testKey := "test-key"

		mockDao := mock.NewMockS3AbiDao(ctrl)
		mockDao.EXPECT().GetObject(gomock.Any(), gomock.Any()).Do(func(bucketName string, key string) {
			if testBucket != bucketName {
				t.Errorf("invalid bucketName is given, expect %s / actual %s\n", testBucket, bucketName)
			}
			if testKey != key {
				t.Errorf("invalid key is given, expect %s / actual %s\n", testKey, key)
			}
		}).Times(1)

		daoRepository := NewS3AbiDaoRepository(l, mockDao)
		if _, err := daoRepository.GetObject(testBucket, testKey); err != nil {
			t.Errorf("test failed: fail to get object")
		}
	})

	t.Run("GetObjectに失敗した場合、falseが返ること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		testBucket := "test-bucket"
		testKey := "test-key"

		mockDao := mock.NewMockS3AbiDao(ctrl)
		mockDao.EXPECT().GetObject(testBucket, testKey).Return(nil, fmt.Errorf("error mock")).Times(1)

		daoRepository := NewS3AbiDaoRepository(l, mockDao)
		if _, err := daoRepository.GetObject(testBucket, testKey); err == nil {
			t.Errorf("test failed: success to get object\n")
		}
	})
}
