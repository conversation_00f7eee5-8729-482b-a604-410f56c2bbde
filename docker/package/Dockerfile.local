ARG IMAGE="golang:1.21.7-alpine3.19"

# build stage image
FROM ${IMAGE} AS build

WORKDIR /src

RUN apk add --upgrade --no-cache build-base && \
    apk add --no-cache git curl && \
    go install github.com/cosmtrek/air@v1.49.0 && \
    go install github.com/go-delve/delve/cmd/dlv@v1.22.0

COPY Makefile go.mod go.sum /src/
RUN make setup

COPY . /src
RUN make prod-build

# runtime image
FROM ${IMAGE}

COPY --from=build ${GOPATH}/bin/ ${GOPATH}/bin/
COPY --from=build src/tmp/bc_monitoring ${GOPATH}/bin/
ENTRYPOINT ["bc_monitoring"]
