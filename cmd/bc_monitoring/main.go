package main

import (
	"github.com/avast/retry-go"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/logger"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/uuid"
	"github.com/sirupsen/logrus"
)

var (
	l         *logrus.Entry
	rtyAttNum = uint(5)
	rtyAtt    = retry.Attempts(rtyAttNum)
	rtyDel    = retry.Delay(3)
	rtyErr    = retry.LastErrorOnly(false)
)

func init() {
	l = logger.NewLogger(uuid.New().ToString())
}

func main() {
	l.Infoln("starting bc monitoring")
	// S3からABIファイルをダウンロードする
	downloadAbi := initializeDownloadAbiInteractor(l)
	if err := downloadAbi.Execute(); err != nil {
		l.<PERSON>alln(err)
	}
	monitorEvent, err := initializeMonitorInteractor(l)
	if err != nil {
		l.<PERSON>(err)
	}
	l.Infoln("started bc monitoring")
	for {
		err := retry.Do(
			func() error { return monitorEvent.Execute() },
			retry.RetryIf(func(err error) bool { return err.Error() == "rpc.wsHandshakeError" }),
			rtyAtt, rtyDel, rtyErr, retry.OnRetry(func(n uint, err error) {
				l.Warnln("restart bc monitoring")
				monitorEvent, err = initializeMonitorInteractor(l)
			}),
		)
		if err != nil {
			l.Warnln("restarting bc monitoring")
		}
	}
}
